#!/usr/bin/env python3
"""
GRETAH Logging Standard Compliance Validator
Validates that all debug() calls comply with the standardized format.
"""

import os
import re
from pathlib import Path

def check_debug_compliance(file_path):
    """Check if debug calls in a file comply with GRETAH Logging Standard"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all debug function calls - more precise pattern
        # Look for debug( at the start of a line or after whitespace, excluding function definitions
        debug_pattern = r'(?:^|\s+)debug\s*\([^)]*(?:\([^)]*\)[^)]*)*\)'
        debug_calls = re.findall(debug_pattern, content, re.MULTILINE | re.DOTALL)

        # Filter out function definitions and other false positives
        actual_debug_calls = []
        for call in debug_calls:
            clean_call = call.strip()
            # Skip function definitions
            if clean_call.startswith('def ') or 'def ' in clean_call:
                continue
            # Skip if it's part of a function signature
            if ': str' in clean_call or 'debug_log_path' in clean_call:
                continue
            actual_debug_calls.append(clean_call)

        debug_calls = actual_debug_calls

        compliant_calls = 0
        non_compliant_calls = []

        for call in debug_calls:
            # Clean up the call for analysis (remove extra whitespace and newlines)
            clean_call = ' '.join(call.split())

            # Check if it has the required parameters: stage, operation
            # Must have both stage= and operation= parameters
            if 'stage=' in clean_call and 'operation=' in clean_call:
                compliant_calls += 1
            else:
                # Truncate long calls for display
                truncated_call = clean_call[:100] + '...' if len(clean_call) > 100 else clean_call
                non_compliant_calls.append(truncated_call)
        
        return {
            'total_calls': len(debug_calls),
            'compliant_calls': compliant_calls,
            'non_compliant_calls': non_compliant_calls,
            'compliance_rate': (compliant_calls / len(debug_calls) * 100) if debug_calls else 100
        }
    except Exception as e:
        return {'error': str(e)}

def main():
    """Main validation function"""
    # Stage files to check
    stage_files = [
        'stages/stage1.py',
        'stages/stage2.py', 
        'stages/stage3.py',
        'stages/stage4.py',
        'stages/stage5.py',
        'stages/stage6.py',
        'stages/stage7.py',
        'stages/stage8.py',
        'stages/stage9.py',
        'stages/stage10.py'
    ]

    print('=' * 80)
    print('GRETAH LOGGING STANDARD COMPLIANCE AUDIT - FINAL VALIDATION')
    print('=' * 80)

    total_files = 0
    compliant_files = 0
    total_debug_calls = 0
    total_compliant_calls = 0

    for stage_file in stage_files:
        if os.path.exists(stage_file):
            result = check_debug_compliance(stage_file)
            total_files += 1
            
            if 'error' not in result:
                total_debug_calls += result['total_calls']
                total_compliant_calls += result['compliant_calls']
                
                status = '✅ COMPLIANT' if result['compliance_rate'] == 100 else '❌ NON-COMPLIANT'
                if result['compliance_rate'] == 100:
                    compliant_files += 1
                    
                print(f'{stage_file:20} | {status:15} | {result["compliant_calls"]:3}/{result["total_calls"]:3} calls ({result["compliance_rate"]:5.1f}%)')
                
                if result['non_compliant_calls']:
                    print(f'  Non-compliant calls:')
                    for call in result['non_compliant_calls'][:3]:  # Show first 3
                        print(f'    - {call}')
                    if len(result['non_compliant_calls']) > 3:
                        print(f'    ... and {len(result["non_compliant_calls"]) - 3} more')
            else:
                print(f'{stage_file:20} | ERROR: {result["error"]}')

    print('=' * 80)
    print('SUMMARY REPORT')
    print('=' * 80)
    print(f'Files Checked:        {total_files}')
    print(f'Compliant Files:      {compliant_files}')
    print(f'Non-Compliant Files:  {total_files - compliant_files}')
    print(f'Overall File Compliance: {(compliant_files / total_files * 100):.1f}%')
    print()
    print(f'Total Debug Calls:    {total_debug_calls}')
    print(f'Compliant Calls:      {total_compliant_calls}')
    print(f'Non-Compliant Calls:  {total_debug_calls - total_compliant_calls}')
    print(f'Overall Call Compliance: {(total_compliant_calls / total_debug_calls * 100):.1f}%')
    print('=' * 80)

    if compliant_files == total_files and total_compliant_calls == total_debug_calls:
        print('🎉 AUDIT COMPLETE: 100% GRETAH Logging Standard Compliance Achieved!')
        return True
    else:
        print('⚠️  AUDIT INCOMPLETE: Additional corrections needed for full compliance')
        return False

if __name__ == '__main__':
    main()
