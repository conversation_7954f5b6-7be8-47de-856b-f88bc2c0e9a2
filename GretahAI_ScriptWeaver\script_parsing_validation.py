#!/usr/bin/env python3
"""
GretahAI ScriptWeaver - Script Parsing Validation Tool
Comprehensive validation of script parsing accuracy across all stages.
"""

import os
import re
import json
import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ai_helpers import clean_llm_response, extract_json_from_response, extract_markdown_from_response
except ImportError as e:
    print(f"Warning: Could not import parsing functions: {e}")
    clean_llm_response = None

def test_clean_llm_response():
    """Test the clean_llm_response function with various input formats"""
    test_cases = [
        # Test case 1: Python code in markdown
        {
            "name": "Python code in markdown",
            "input": """Here's the generated script:

```python
import pytest
from selenium import webdriver

def test_login():
    driver = webdriver.Chrome()
    driver.get("https://example.com")
    # Test implementation
    driver.quit()
```

This script should work correctly.""",
            "expected_format": "python",
            "should_contain": ["import pytest", "webdriver.Chrome()", "driver.quit()"],
            "should_not_contain": ["Here's the generated", "This script should"]
        },

        # Test case 1b: Real GretahAI generated script format
        {
            "name": "Real GretahAI script format",
            "input": """I'll generate a comprehensive test script for your test case. Here's the automation script:

```python
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

@pytest.fixture
def browser():
    \"\"\"Browser fixture for test execution\"\"\"
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    yield driver
    driver.quit()

def test_login_functionality(browser):
    \"\"\"Test Case: Verify login functionality\"\"\"
    try:
        # Step 1: Navigate to login page
        browser.get("https://example.com/login")
        wait = WebDriverWait(browser, 10)

        # Step 2: Enter username
        username_field = wait.until(EC.presence_of_element_located((By.ID, "username")))
        username_field.clear()
        username_field.send_keys("<EMAIL>")

        # Step 3: Enter password
        password_field = browser.find_element(By.ID, "password")
        password_field.clear()
        password_field.send_keys("password123")

        # Step 4: Click login button
        login_button = browser.find_element(By.XPATH, "//button[@type='submit']")
        login_button.click()

        # Step 5: Verify successful login
        success_element = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "dashboard")))
        assert success_element.is_displayed(), "Login was not successful"

        print("✅ Test passed: Login functionality verified")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        raise
```

This script implements the complete test case with proper error handling and assertions.""",
            "expected_format": "python",
            "should_contain": ["import pytest", "@pytest.fixture", "def test_login_functionality", "WebDriverWait", "assert success_element.is_displayed()"],
            "should_not_contain": ["I'll generate a comprehensive", "This script implements"]
        },
        
        # Test case 2: JSON response
        {
            "name": "JSON response in markdown",
            "input": """The analysis results are:

```json
{
    "status": "success",
    "steps": [
        {"step_no": 1, "action": "navigate", "target": "login_page"},
        {"step_no": 2, "action": "click", "target": "login_button"}
    ]
}
```

Analysis complete.""",
            "expected_format": "json",
            "should_contain": ['"status": "success"', '"action": "navigate"'],
            "should_not_contain": ["The analysis results", "Analysis complete"]
        },
        
        # Test case 3: Code without language specifier
        {
            "name": "Code without language specifier",
            "input": """```
def test_function():
    assert True
```""",
            "expected_format": "python",
            "should_contain": ["def test_function()", "assert True"],
            "should_not_contain": []
        },
        
        # Test case 4: Multiple code blocks
        {
            "name": "Multiple code blocks",
            "input": """First, here's the JSON:
```json
{"test": "data"}
```

Then the Python code:
```python
def main():
    print("Hello")
```""",
            "expected_format": "python",
            "should_contain": ["def main()", 'print("Hello")'],
            "should_not_contain": ['"test": "data"', "First, here's"]
        },
        
        # Test case 5: No code blocks
        {
            "name": "Plain text without code blocks",
            "input": "This is just plain text without any code blocks.",
            "expected_format": "python",
            "should_contain": ["This is just plain text"],
            "should_not_contain": []
        },

        # Test case 6: Script with optimization comments
        {
            "name": "Optimized script with AI comments",
            "input": """Based on your requirements, I've optimized the script with the following improvements:

1. Enhanced error handling
2. Better locator strategies
3. Improved assertions

```python
# Optimized Test Script - Generated by GretahAI ScriptWeaver
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class TestLoginOptimized:
    \"\"\"Optimized test class for login functionality\"\"\"

    @pytest.fixture(autouse=True)
    def setup_teardown(self):
        \"\"\"Setup and teardown for each test\"\"\"
        self.driver = webdriver.Chrome()
        self.wait = WebDriverWait(self.driver, 10)
        yield
        self.driver.quit()

    def test_valid_login(self):
        \"\"\"Test valid user login with enhanced error handling\"\"\"
        try:
            # Navigate with timeout handling
            self.driver.get("https://example.com")

            # Enhanced element location with explicit waits
            username = self.wait.until(
                EC.element_to_be_clickable((By.ID, "username"))
            )
            username.send_keys("<EMAIL>")

            password = self.driver.find_element(By.ID, "password")
            password.send_keys("validpassword")

            # Submit with multiple locator strategies
            submit_btn = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            submit_btn.click()

            # Robust success verification
            dashboard = self.wait.until(
                EC.presence_of_element_located((By.CLASS_NAME, "dashboard"))
            )
            assert dashboard.is_displayed()

        except Exception as e:
            pytest.fail(f"Login test failed: {e}")
```

The optimized script includes better practices and more robust element handling.""",
            "expected_format": "python",
            "should_contain": ["class TestLoginOptimized", "@pytest.fixture(autouse=True)", "WebDriverWait", "pytest.fail"],
            "should_not_contain": ["Based on your requirements", "The optimized script includes"]
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        if clean_llm_response is None:
            results.append({
                "name": test_case["name"],
                "status": "SKIPPED",
                "reason": "clean_llm_response function not available"
            })
            continue
            
        try:
            # Test the function
            result = clean_llm_response(test_case["input"], test_case["expected_format"])
            
            # Check if expected content is present
            contains_expected = all(content in result for content in test_case["should_contain"])
            
            # Check if unwanted content is absent
            excludes_unwanted = all(content not in result for content in test_case["should_not_contain"])
            
            status = "PASS" if contains_expected and excludes_unwanted else "FAIL"
            
            results.append({
                "name": test_case["name"],
                "status": status,
                "input_length": len(test_case["input"]),
                "output_length": len(result),
                "contains_expected": contains_expected,
                "excludes_unwanted": excludes_unwanted,
                "output_preview": result[:100] + "..." if len(result) > 100 else result
            })
            
        except Exception as e:
            results.append({
                "name": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })
    
    return results

def test_script_syntax_validation():
    """Test that parsed scripts have valid Python syntax"""
    if clean_llm_response is None:
        return [{"name": "Syntax validation", "status": "SKIPPED", "reason": "clean_llm_response not available"}]

    # Test with a complex AI-generated script
    complex_script_input = """Here's your comprehensive test automation script:

```python
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import time
import json

@pytest.fixture(scope="session")
def browser_config():
    \"\"\"Browser configuration fixture\"\"\"
    return {
        "headless": False,
        "window_size": (1920, 1080),
        "timeout": 30
    }

@pytest.fixture
def browser(browser_config):
    \"\"\"Browser fixture with configuration\"\"\"
    from selenium.webdriver.chrome.options import Options

    options = Options()
    if browser_config["headless"]:
        options.add_argument("--headless")
    options.add_argument(f"--window-size={browser_config['window_size'][0]},{browser_config['window_size'][1]}")

    driver = webdriver.Chrome(options=options)
    driver.implicitly_wait(browser_config["timeout"])

    yield driver
    driver.quit()

class TestECommerceWorkflow:
    \"\"\"Test class for e-commerce workflow automation\"\"\"

    def test_complete_purchase_flow(self, browser):
        \"\"\"Test complete purchase workflow from login to checkout\"\"\"
        wait = WebDriverWait(browser, 10)

        try:
            # Step 1: Navigate to homepage
            browser.get("https://example-store.com")
            assert "Example Store" in browser.title

            # Step 2: Login
            login_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Login")))
            login_link.click()

            username_field = wait.until(EC.presence_of_element_located((By.ID, "email")))
            username_field.send_keys("<EMAIL>")

            password_field = browser.find_element(By.ID, "password")
            password_field.send_keys("testpassword123")

            login_button = browser.find_element(By.CSS_SELECTOR, "button[type='submit']")
            login_button.click()

            # Verify login success
            user_menu = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "user-menu")))
            assert user_menu.is_displayed()

            # Step 3: Search for product
            search_box = browser.find_element(By.NAME, "search")
            search_box.send_keys("laptop")
            search_box.submit()

            # Step 4: Select product
            first_product = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".product-item:first-child")))
            first_product.click()

            # Step 5: Add to cart
            add_to_cart_btn = wait.until(EC.element_to_be_clickable((By.ID, "add-to-cart")))
            add_to_cart_btn.click()

            # Verify item added
            cart_count = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "cart-count")))
            assert int(cart_count.text) > 0

            # Step 6: Proceed to checkout
            cart_icon = browser.find_element(By.CLASS_NAME, "cart-icon")
            cart_icon.click()

            checkout_btn = wait.until(EC.element_to_be_clickable((By.ID, "checkout")))
            checkout_btn.click()

            # Step 7: Fill shipping information
            shipping_form = wait.until(EC.presence_of_element_located((By.ID, "shipping-form")))

            browser.find_element(By.ID, "first-name").send_keys("John")
            browser.find_element(By.ID, "last-name").send_keys("Doe")
            browser.find_element(By.ID, "address").send_keys("123 Main St")
            browser.find_element(By.ID, "city").send_keys("Anytown")
            browser.find_element(By.ID, "zip").send_keys("12345")

            # Step 8: Complete purchase
            place_order_btn = browser.find_element(By.ID, "place-order")
            place_order_btn.click()

            # Verify order confirmation
            confirmation = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "order-confirmation")))
            assert "Thank you for your order" in confirmation.text

            print("✅ Complete purchase workflow test passed")

        except Exception as e:
            print(f"❌ Test failed at step: {e}")
            browser.save_screenshot("test_failure.png")
            raise
```

This script covers the complete e-commerce workflow with proper error handling."""

    try:
        # Parse the script
        parsed_script = clean_llm_response(complex_script_input, "python")

        # Test syntax validity
        import ast
        ast.parse(parsed_script)

        # Check for essential components
        has_imports = "import pytest" in parsed_script
        has_fixtures = "@pytest.fixture" in parsed_script
        has_test_class = "class Test" in parsed_script
        has_test_method = "def test_" in parsed_script
        has_assertions = "assert " in parsed_script

        return [{
            "name": "Complex script syntax validation",
            "status": "PASS",
            "has_imports": has_imports,
            "has_fixtures": has_fixtures,
            "has_test_class": has_test_class,
            "has_test_method": has_test_method,
            "has_assertions": has_assertions,
            "parsed_length": len(parsed_script),
            "syntax_valid": True
        }]

    except SyntaxError as e:
        return [{
            "name": "Complex script syntax validation",
            "status": "FAIL",
            "error": f"Syntax error: {e}",
            "syntax_valid": False
        }]
    except Exception as e:
        return [{
            "name": "Complex script syntax validation",
            "status": "ERROR",
            "error": str(e),
            "syntax_valid": False
        }]

def find_parsing_usage_in_stages():
    """Find all usages of parsing functions in stage files"""
    stage_files = [f"stages/stage{i}.py" for i in range(1, 11)]
    parsing_usage = []
    
    parsing_functions = [
        "clean_llm_response",
        "extract_json_from_response", 
        "extract_markdown_from_response"
    ]
    
    for stage_file in stage_files:
        if not os.path.exists(stage_file):
            continue
            
        try:
            with open(stage_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for func_name in parsing_functions:
                # Find all occurrences of the function
                pattern = rf'{func_name}\s*\([^)]*\)'
                matches = re.findall(pattern, content)
                
                for match in matches:
                    # Get line number
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if match in line:
                            parsing_usage.append({
                                "file": stage_file,
                                "function": func_name,
                                "line_number": i + 1,
                                "usage": match.strip(),
                                "context": line.strip()
                            })
                            break
                            
        except Exception as e:
            parsing_usage.append({
                "file": stage_file,
                "function": "ERROR",
                "error": str(e)
            })
    
    return parsing_usage

def validate_script_generation_stages():
    """Validate script generation and parsing in key stages"""
    validation_results = []
    
    # Stage 6: Script generation
    stage6_path = "stages/stage6.py"
    if os.path.exists(stage6_path):
        with open(stage6_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for script generation patterns
        has_script_generation = "generate_test_script" in content
        has_parsing = "clean_llm_response" in content
        
        validation_results.append({
            "stage": "Stage 6",
            "purpose": "Script Generation",
            "has_generation": has_script_generation,
            "has_parsing": has_parsing,
            "status": "PASS" if has_script_generation else "NEEDS_REVIEW"
        })
    
    # Stage 8: Script optimization
    stage8_path = "stages/stage8.py"
    if os.path.exists(stage8_path):
        with open(stage8_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_optimization = "optimize" in content.lower()
        has_parsing = "clean_llm_response" in content
        
        validation_results.append({
            "stage": "Stage 8", 
            "purpose": "Script Optimization",
            "has_optimization": has_optimization,
            "has_parsing": has_parsing,
            "status": "PASS" if has_optimization else "NEEDS_REVIEW"
        })
    
    # Stage 10: Template-based generation
    stage10_path = "stages/stage10.py"
    if os.path.exists(stage10_path):
        with open(stage10_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_template_generation = "template" in content.lower()
        has_parsing = "clean_llm_response" in content
        
        validation_results.append({
            "stage": "Stage 10",
            "purpose": "Template-based Generation", 
            "has_template_generation": has_template_generation,
            "has_parsing": has_parsing,
            "status": "PASS" if has_template_generation and has_parsing else "NEEDS_REVIEW"
        })
    
    return validation_results

def main():
    """Main validation function"""
    print("=" * 80)
    print("GRETAH SCRIPTWEAVER - SCRIPT PARSING VALIDATION REPORT")
    print("=" * 80)
    
    # Test 1: clean_llm_response function testing
    print("\n1. CLEAN_LLM_RESPONSE FUNCTION TESTING")
    print("-" * 50)
    
    test_results = test_clean_llm_response()
    
    passed_tests = sum(1 for r in test_results if r["status"] == "PASS")
    total_tests = len(test_results)
    
    for result in test_results:
        status_icon = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
        print(f"{status_icon} {result['name']}: {result['status']}")
        
        if result["status"] == "FAIL":
            print(f"   Expected content present: {result.get('contains_expected', 'N/A')}")
            print(f"   Unwanted content excluded: {result.get('excludes_unwanted', 'N/A')}")
        elif result["status"] == "ERROR":
            print(f"   Error: {result.get('error', 'Unknown error')}")
    
    print(f"\nFunction Test Results: {passed_tests}/{total_tests} tests passed")

    # Test 1b: Script syntax validation
    print("\n1b. SCRIPT SYNTAX VALIDATION")
    print("-" * 50)

    syntax_results = test_script_syntax_validation()

    for result in syntax_results:
        status_icon = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
        print(f"{status_icon} {result['name']}: {result['status']}")

        if result["status"] == "PASS":
            print(f"   Parsed length: {result.get('parsed_length', 'N/A')} characters")
            print(f"   Has imports: {result.get('has_imports', False)}")
            print(f"   Has fixtures: {result.get('has_fixtures', False)}")
            print(f"   Has test class: {result.get('has_test_class', False)}")
            print(f"   Has test method: {result.get('has_test_method', False)}")
            print(f"   Has assertions: {result.get('has_assertions', False)}")
        elif result["status"] in ["FAIL", "ERROR"]:
            print(f"   Error: {result.get('error', 'Unknown error')}")

    syntax_passed = sum(1 for r in syntax_results if r["status"] == "PASS")
    syntax_total = len(syntax_results)

    # Test 2: Parsing usage in stages
    print("\n2. PARSING FUNCTION USAGE IN STAGES")
    print("-" * 50)
    
    usage_results = find_parsing_usage_in_stages()
    
    if usage_results:
        for usage in usage_results:
            if "error" in usage:
                print(f"❌ {usage['file']}: Error - {usage['error']}")
            else:
                print(f"📄 {usage['file']}:{usage['line_number']} - {usage['function']}()")
    else:
        print("⚠️  No parsing function usage found in stage files")
    
    # Test 3: Script generation stage validation
    print("\n3. SCRIPT GENERATION STAGE VALIDATION")
    print("-" * 50)
    
    stage_results = validate_script_generation_stages()
    
    for result in stage_results:
        status_icon = "✅" if result["status"] == "PASS" else "⚠️"
        print(f"{status_icon} {result['stage']} ({result['purpose']}): {result['status']}")
    
    # Summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    function_test_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    syntax_test_rate = (syntax_passed / syntax_total * 100) if syntax_total > 0 else 0
    usage_count = len([u for u in usage_results if "error" not in u])
    stage_pass_count = sum(1 for r in stage_results if r["status"] == "PASS")

    print(f"Function Tests:     {passed_tests}/{total_tests} passed ({function_test_rate:.1f}%)")
    print(f"Syntax Tests:       {syntax_passed}/{syntax_total} passed ({syntax_test_rate:.1f}%)")
    print(f"Usage Found:        {usage_count} parsing function calls across stages")
    print(f"Stage Validation:   {stage_pass_count}/{len(stage_results)} stages validated")

    overall_status = "PASS" if function_test_rate >= 80 and syntax_test_rate >= 80 and usage_count > 0 else "NEEDS_ATTENTION"
    status_icon = "✅" if overall_status == "PASS" else "⚠️"
    
    print(f"\nOverall Status:     {status_icon} {overall_status}")
    
    if overall_status == "NEEDS_ATTENTION":
        print("\nRecommendations:")
        if function_test_rate < 80:
            print("- Review clean_llm_response function implementation")
        if syntax_test_rate < 80:
            print("- Review script parsing for syntax preservation")
        if usage_count == 0:
            print("- Verify parsing functions are being used in script generation")

if __name__ == "__main__":
    main()
